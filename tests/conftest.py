"""Pytest configuration and fixtures."""

import os
from unittest.mock import AsyncMock, MagicMock
from typing import AsyncGenerator

import pytest
import httpx
from pydantic_settings import BaseSettings

from albatross_kiotviet_mcp.config import Settings
from albatross_kiotviet_mcp.auth import AuthClient
from albatross_kiotviet_mcp.api import CategoriesClient


class TestSettings(BaseSettings):
    """Test settings with default values."""
    
    kiotviet_client_id: str = "test_client_id"
    kiotviet_client_secret: str = "test_client_secret"
    kiotviet_retailer: str = "test_retailer"
    kiotviet_base_url: str = "https://api.test.com"
    kiotviet_auth_url: str = "https://auth.test.com/token"
    log_level: str = "DEBUG"
    debug: bool = True
    token_cache_ttl: int = 3600
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    server_name: str = "test-server"
    server_version: str = "0.1.0"


@pytest.fixture
def test_settings() -> Settings:
    """Provide test settings."""
    return TestSettings()


@pytest.fixture
async def mock_auth_client(test_settings: Settings) -> AsyncGenerator[AsyncMock, None]:
    """Provide a mocked authentication client."""
    mock_client = AsyncMock(spec=AuthClient)
    mock_client.settings = test_settings
    
    # Mock token cache
    mock_token = MagicMock()
    mock_token.access_token = "test_access_token"
    mock_token.token_type = "Bearer"
    mock_token.authorization_header = "Bearer test_access_token"
    mock_token.is_expired = False
    
    mock_client.get_valid_token.return_value = mock_token
    
    yield mock_client


@pytest.fixture
async def mock_categories_client(
    test_settings: Settings, 
    mock_auth_client: AsyncMock
) -> AsyncGenerator[AsyncMock, None]:
    """Provide a mocked categories client."""
    mock_client = AsyncMock(spec=CategoriesClient)
    mock_client.settings = test_settings
    mock_client.auth_client = mock_auth_client
    
    yield mock_client


@pytest.fixture
def mock_httpx_response() -> MagicMock:
    """Provide a mocked httpx response."""
    mock_response = MagicMock(spec=httpx.Response)
    mock_response.status_code = 200
    mock_response.json.return_value = {"data": [], "total": 0}
    mock_response.text = '{"data": [], "total": 0}'
    
    return mock_response


@pytest.fixture(autouse=True)
def setup_test_env():
    """Set up test environment variables."""
    test_env = {
        "KIOTVIET_CLIENT_ID": "test_client_id",
        "KIOTVIET_CLIENT_SECRET": "test_client_secret", 
        "KIOTVIET_RETAILER": "test_retailer",
        "LOG_LEVEL": "DEBUG",
        "DEBUG": "true",
    }
    
    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value
    
    yield
    
    # Clean up environment variables
    for key in test_env.keys():
        os.environ.pop(key, None)
