"""Tests for configuration management."""

import os
import pytest
from pydantic import ValidationError

from albatross_kiotviet_mcp.config import Settings, get_settings


class TestSettings:
    """Test Settings class."""
    
    def test_settings_with_valid_env_vars(self):
        """Test settings creation with valid environment variables."""
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
        )
        
        assert settings.kiotviet_client_id == "test_id"
        assert settings.kiotviet_client_secret == "test_secret"
        assert settings.kiotviet_retailer == "test_retailer"
        assert settings.kiotviet_base_url == "https://public.kiotapi.com"
        assert settings.log_level == "INFO"
        assert settings.debug is False
        assert settings.token_cache_ttl == 3600
    
    def test_settings_missing_required_fields(self):
        """Test settings validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            Settings()
        
        errors = exc_info.value.errors()
        required_fields = {"kiotviet_client_id", "kiotviet_client_secret", "kiotviet_retailer"}
        error_fields = {error["loc"][0] for error in errors}
        
        assert required_fields.issubset(error_fields)
    
    def test_log_level_validation(self):
        """Test log level validation."""
        # Valid log level
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            log_level="DEBUG",
        )
        assert settings.log_level == "DEBUG"
        
        # Invalid log level
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                log_level="INVALID",
            )
        
        errors = exc_info.value.errors()
        assert any("log_level" in str(error) for error in errors)
    
    def test_token_cache_ttl_validation(self):
        """Test token cache TTL validation."""
        # Valid TTL
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            token_cache_ttl=1800,
        )
        assert settings.token_cache_ttl == 1800
        
        # Invalid TTL (negative)
        with pytest.raises(ValidationError):
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                token_cache_ttl=-1,
            )
        
        # Invalid TTL (zero)
        with pytest.raises(ValidationError):
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                token_cache_ttl=0,
            )
    
    def test_request_timeout_validation(self):
        """Test request timeout validation."""
        # Valid timeout
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            request_timeout=60,
        )
        assert settings.request_timeout == 60
        
        # Invalid timeout
        with pytest.raises(ValidationError):
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                request_timeout=-1,
            )
    
    def test_max_retries_validation(self):
        """Test max retries validation."""
        # Valid retries
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            max_retries=5,
        )
        assert settings.max_retries == 5
        
        # Valid retries (zero)
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            max_retries=0,
        )
        assert settings.max_retries == 0
        
        # Invalid retries
        with pytest.raises(ValidationError):
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                max_retries=-1,
            )
    
    def test_retry_delay_validation(self):
        """Test retry delay validation."""
        # Valid delay
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            retry_delay=2.5,
        )
        assert settings.retry_delay == 2.5
        
        # Valid delay (zero)
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
            retry_delay=0.0,
        )
        assert settings.retry_delay == 0.0
        
        # Invalid delay
        with pytest.raises(ValidationError):
            Settings(
                kiotviet_client_id="test_id",
                kiotviet_client_secret="test_secret",
                kiotviet_retailer="test_retailer",
                retry_delay=-1.0,
            )


class TestGetSettings:
    """Test get_settings function."""
    
    def test_get_settings_caching(self):
        """Test that get_settings returns cached instance."""
        # Clear any existing cache
        get_settings.cache_clear()
        
        settings1 = get_settings()
        settings2 = get_settings()
        
        # Should be the same instance due to caching
        assert settings1 is settings2
