"""Tests for authentication models."""

from datetime import datetime, timedelta

import pytest
from pydantic import ValidationError

from albatross_kiotviet_mcp.auth.models import TokenResponse, TokenCache


class TestTokenResponse:
    """Test TokenResponse model."""
    
    def test_token_response_creation(self):
        """Test creating a valid token response."""
        token_response = TokenResponse(
            access_token="test_token",
            token_type="Bearer",
            expires_in=3600,
            scope="PublicApi.Access",
        )
        
        assert token_response.access_token == "test_token"
        assert token_response.token_type == "Bearer"
        assert token_response.expires_in == 3600
        assert token_response.scope == "PublicApi.Access"
    
    def test_token_response_defaults(self):
        """Test token response with default values."""
        token_response = TokenResponse(
            access_token="test_token",
            expires_in=3600,
        )
        
        assert token_response.token_type == "Bearer"
        assert token_response.scope is None
    
    def test_token_response_validation(self):
        """Test token response validation."""
        # Missing required fields
        with pytest.raises(ValidationError):
            TokenResponse()
        
        # Invalid expires_in (negative)
        with pytest.raises(ValidationError):
            TokenResponse(
                access_token="test_token",
                expires_in=-1,
            )
        
        # Invalid expires_in (zero)
        with pytest.raises(ValidationError):
            TokenResponse(
                access_token="test_token",
                expires_in=0,
            )


class TestTokenCache:
    """Test TokenCache model."""
    
    def test_token_cache_creation(self):
        """Test creating a valid token cache."""
        expires_at = datetime.utcnow() + timedelta(hours=1)
        
        token_cache = TokenCache(
            access_token="test_token",
            token_type="Bearer",
            expires_at=expires_at,
            scope="PublicApi.Access",
        )
        
        assert token_cache.access_token == "test_token"
        assert token_cache.token_type == "Bearer"
        assert token_cache.expires_at == expires_at
        assert token_cache.scope == "PublicApi.Access"
    
    def test_token_cache_defaults(self):
        """Test token cache with default values."""
        expires_at = datetime.utcnow() + timedelta(hours=1)
        
        token_cache = TokenCache(
            access_token="test_token",
            expires_at=expires_at,
        )
        
        assert token_cache.token_type == "Bearer"
        assert token_cache.scope is None
    
    def test_from_token_response(self):
        """Test creating token cache from token response."""
        token_response = TokenResponse(
            access_token="test_token",
            token_type="Bearer",
            expires_in=3600,
            scope="PublicApi.Access",
        )
        
        before_creation = datetime.utcnow()
        token_cache = TokenCache.from_token_response(token_response)
        after_creation = datetime.utcnow()
        
        assert token_cache.access_token == "test_token"
        assert token_cache.token_type == "Bearer"
        assert token_cache.scope == "PublicApi.Access"
        
        # Check that expires_at is set correctly (with buffer)
        expected_expires_at = before_creation + timedelta(seconds=3600 - 300)  # 5 min buffer
        assert token_cache.expires_at >= expected_expires_at
        assert token_cache.expires_at <= after_creation + timedelta(seconds=3600 - 300)
    
    def test_from_token_response_with_custom_buffer(self):
        """Test creating token cache with custom buffer."""
        token_response = TokenResponse(
            access_token="test_token",
            expires_in=3600,
        )
        
        before_creation = datetime.utcnow()
        token_cache = TokenCache.from_token_response(token_response, buffer_seconds=600)
        
        # Check that expires_at uses custom buffer
        expected_expires_at = before_creation + timedelta(seconds=3600 - 600)  # 10 min buffer
        assert token_cache.expires_at >= expected_expires_at
    
    def test_is_expired_property(self):
        """Test is_expired property."""
        # Not expired token
        future_time = datetime.utcnow() + timedelta(hours=1)
        token_cache = TokenCache(
            access_token="test_token",
            expires_at=future_time,
        )
        assert not token_cache.is_expired
        
        # Expired token
        past_time = datetime.utcnow() - timedelta(hours=1)
        token_cache = TokenCache(
            access_token="test_token",
            expires_at=past_time,
        )
        assert token_cache.is_expired
        
        # Token expiring now (should be considered expired)
        now = datetime.utcnow()
        token_cache = TokenCache(
            access_token="test_token",
            expires_at=now,
        )
        assert token_cache.is_expired
    
    def test_authorization_header_property(self):
        """Test authorization_header property."""
        expires_at = datetime.utcnow() + timedelta(hours=1)
        
        token_cache = TokenCache(
            access_token="test_token_123",
            token_type="Bearer",
            expires_at=expires_at,
        )
        
        assert token_cache.authorization_header == "Bearer test_token_123"
        
        # Test with different token type
        token_cache = TokenCache(
            access_token="test_token_456",
            token_type="Custom",
            expires_at=expires_at,
        )
        
        assert token_cache.authorization_header == "Custom test_token_456"
