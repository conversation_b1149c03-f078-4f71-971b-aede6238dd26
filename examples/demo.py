#!/usr/bin/env python3
"""Demo script showing KiotViet MCP server capabilities."""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from albatross_kiotviet_mcp.config import get_settings
from albatross_kiotviet_mcp.auth import AuthClient
from albatross_kiotviet_mcp.api import CategoriesClient
from albatross_kiotviet_mcp.utils import setup_logging


async def demo_authentication():
    """Demo authentication functionality."""
    print("🔐 Testing Authentication...")
    
    settings = get_settings()
    logger = setup_logging(level=settings.log_level, debug=settings.debug)
    
    async with AuthClient(settings) as auth_client:
        try:
            # Get a valid token
            token = await auth_client.get_valid_token()
            print(f"✅ Successfully obtained access token")
            print(f"   Token type: {token.token_type}")
            print(f"   Expires at: {token.expires_at}")
            print(f"   Is expired: {token.is_expired}")
            
        except Exception as e:
            print(f"❌ Authentication failed: {e}")
            return False
    
    return True


async def demo_categories_api():
    """Demo Categories API functionality."""
    print("\n📂 Testing Categories API...")
    
    settings = get_settings()
    
    async with AuthClient(settings) as auth_client:
        async with CategoriesClient(settings, auth_client) as categories_client:
            try:
                # List categories
                print("   📋 Listing categories...")
                response = await categories_client.list_categories(
                    page_size=5,
                    current_item=0
                )
                
                print(f"   ✅ Found {len(response.data)} categories (total: {response.total})")
                for category in response.data[:3]:  # Show first 3
                    print(f"      - {category.category_name} (ID: {category.id})")
                
                if response.data:
                    # Get specific category
                    first_category = response.data[0]
                    print(f"\n   🔍 Getting category details for ID {first_category.id}...")
                    
                    category = await categories_client.get_category(first_category.id)
                    if category:
                        print(f"   ✅ Category details:")
                        print(f"      Name: {category.category_name}")
                        print(f"      Code: {category.category_code}")
                        print(f"      Active: {category.is_active}")
                        print(f"      Has children: {category.has_child}")
                
                # Search categories
                print(f"\n   🔎 Searching categories...")
                search_response = await categories_client.search_categories(
                    search_term="",  # Empty search to get all
                    page_size=3
                )
                print(f"   ✅ Search returned {len(search_response.data)} categories")
                
                # Get hierarchy
                print(f"\n   🌳 Getting category hierarchy...")
                hierarchy = await categories_client.get_category_hierarchy(
                    parent_id=None,  # Root categories
                    include_inactive=False
                )
                print(f"   ✅ Found {len(hierarchy)} root categories")
                
            except Exception as e:
                print(f"   ❌ Categories API failed: {e}")
                return False
    
    return True


async def demo_error_handling():
    """Demo error handling."""
    print("\n⚠️  Testing Error Handling...")
    
    settings = get_settings()
    
    # Test with invalid credentials
    invalid_settings = settings.model_copy()
    invalid_settings.kiotviet_client_secret = "invalid_secret"
    
    async with AuthClient(invalid_settings) as auth_client:
        try:
            await auth_client.get_valid_token()
            print("   ❌ Expected authentication to fail with invalid credentials")
            return False
        except Exception as e:
            print(f"   ✅ Correctly handled invalid credentials: {type(e).__name__}")
    
    return True


async def main():
    """Run the demo."""
    print("🚀 KiotViet MCP Server Demo")
    print("=" * 50)
    
    try:
        # Check configuration
        settings = get_settings()
        print(f"📋 Configuration loaded:")
        print(f"   Server: {settings.server_name} v{settings.server_version}")
        print(f"   Retailer: {settings.kiotviet_retailer}")
        print(f"   Base URL: {settings.kiotviet_base_url}")
        print(f"   Log Level: {settings.log_level}")
        
        # Run demos
        demos = [
            ("Authentication", demo_authentication),
            ("Categories API", demo_categories_api),
            ("Error Handling", demo_error_handling),
        ]
        
        results = []
        for name, demo_func in demos:
            try:
                result = await demo_func()
                results.append((name, result))
            except Exception as e:
                print(f"❌ {name} demo failed with exception: {e}")
                results.append((name, False))
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Demo Results:")
        
        passed = 0
        for name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(results)} demos passed")
        
        if passed == len(results):
            print("🎉 All demos completed successfully!")
            print("\n💡 The KiotViet MCP server is ready to use!")
            print("   Run: python3.10 -m albatross_kiotviet_mcp.cli run")
        else:
            print("⚠️  Some demos failed. Please check your configuration and credentials.")
            return 1
            
    except Exception as e:
        print(f"❌ Demo failed with exception: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
