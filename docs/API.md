# API Documentation

## Overview

The KiotViet MCP Server provides MCP tools for interacting with the KiotViet Public API. All tools handle authentication automatically and provide structured responses.

## Available Tools

### Categories Management

#### `list_categories`

List categories with optional filtering and pagination.

**Parameters:**
- `page_size` (int, optional): Number of items per page (1-100, default: 20)
- `current_item` (int, optional): Starting item index (default: 0)
- `hierarchical_data` (bool, optional): Return hierarchical structure (default: false)
- `include_inactive` (bool, optional): Include inactive categories (default: false)
- `parent_id` (int, optional): Filter by parent category ID
- `search_term` (str, optional): Search term for name or code

**Response:**
```json
{
  "categories": [
    {
      "id": 123,
      "category_name": "Electronics",
      "category_code": "ELEC",
      "description": "Electronic products",
      "parent_id": null,
      "has_child": true,
      "is_active": true,
      "created_date": "2024-01-01T00:00:00Z",
      "modified_date": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 50,
    "page_size": 20,
    "current_item": 0,
    "has_more": true,
    "next_current_item": 20
  }
}
```

#### `search_categories`

Search categories by name or code.

**Parameters:**
- `search_term` (str, required): Search term for category name or code
- `page_size` (int, optional): Number of items per page (1-100, default: 20)
- `current_item` (int, optional): Starting item index (default: 0)
- `include_inactive` (bool, optional): Include inactive categories (default: false)

**Response:**
```json
{
  "categories": [...],
  "search_term": "electronics",
  "pagination": {...}
}
```

#### `get_category`

Get a specific category by ID.

**Parameters:**
- `category_id` (int, required): Category ID to retrieve

**Response:**
```json
{
  "category": {
    "id": 123,
    "category_name": "Electronics",
    ...
  },
  "found": true
}
```

#### `get_category_hierarchy`

Get categories in hierarchical structure.

**Parameters:**
- `parent_id` (int, optional): Parent category ID (None for root categories)
- `include_inactive` (bool, optional): Include inactive categories (default: false)

**Response:**
```json
{
  "categories": [...],
  "parent_id": null,
  "hierarchical": true,
  "count": 10
}
```

## Error Handling

All tools return structured error responses when operations fail:

```json
{
  "error": "Failed to list categories: Authentication failed",
  "details": {
    "status_code": 401,
    "response": "Unauthorized"
  }
}
```

## Authentication

Authentication is handled automatically by the server:

1. **Token Acquisition**: Obtains access tokens using client credentials
2. **Token Caching**: Caches tokens to avoid unnecessary requests
3. **Automatic Refresh**: Refreshes expired tokens automatically
4. **Retry Logic**: Retries failed requests with exponential backoff

## Rate Limiting

The server implements retry logic with exponential backoff to handle rate limiting:

- **Max Retries**: 3 attempts
- **Base Delay**: 1 second
- **Max Delay**: 10 seconds
- **Exponential Multiplier**: 2x

## Data Models

### Category

```json
{
  "id": 123,
  "category_name": "Category Name",
  "category_code": "CODE",
  "description": "Category description",
  "parent_id": 456,
  "has_child": true,
  "is_active": true,
  "created_date": "2024-01-01T00:00:00Z",
  "modified_date": "2024-01-01T00:00:00Z"
}
```

### Pagination

```json
{
  "total": 100,
  "page_size": 20,
  "current_item": 0,
  "has_more": true,
  "next_current_item": 20
}
```
