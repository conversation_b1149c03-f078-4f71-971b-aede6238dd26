# Development Guide

## Setup Development Environment

### Prerequisites

- Python 3.9 or higher
- Git
- KiotViet API credentials

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd albatross-kiotviet-mcp
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install development dependencies:
```bash
pip install -e ".[dev]"
```

4. Install pre-commit hooks:
```bash
pre-commit install
```

5. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

## Project Structure

```
src/albatross_kiotviet_mcp/
├── __init__.py              # Package initialization
├── cli.py                   # CLI entry point
├── server.py               # MCP server implementation
├── config/
│   ├── __init__.py
│   └── settings.py         # Configuration management
├── auth/
│   ├── __init__.py
│   ├── models.py           # Authentication data models
│   └── client.py           # Token management
├── api/
│   ├── __init__.py
│   ├── base.py             # Base API client
│   ├── categories.py       # Categories API implementation
│   └── models/
│       ├── __init__.py
│       ├── common.py       # Common API models
│       └── categories.py   # Categories-specific models
└── utils/
    ├── __init__.py
    ├── logging.py          # Logging configuration
    └── exceptions.py       # Custom exceptions

tests/
├── unit/                   # Unit tests
└── integration/            # Integration tests
```

## Code Quality

### Formatting and Linting

```bash
# Format code
black src tests
isort src tests

# Type checking
mypy src

# Run all quality checks
pre-commit run --all-files
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov

# Run specific test types
pytest -m unit
pytest -m integration

# Run tests with verbose output
pytest -v
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test API integration with mocked responses
- **End-to-End Tests**: Test complete workflows (requires real API credentials)

## Adding New API Endpoints

### 1. Create Data Models

Add models in `src/albatross_kiotviet_mcp/api/models/`:

```python
# new_endpoint.py
from pydantic import BaseModel, Field
from .common import PaginationRequest, PaginationResponse

class NewItem(BaseModel):
    id: int
    name: str
    # ... other fields

class NewItemListRequest(PaginationRequest):
    # ... specific request fields
    pass

class NewItemListResponse(PaginationResponse[NewItem]):
    data: List[NewItem] = Field(default_factory=list)
```

### 2. Create API Client

Add client in `src/albatross_kiotviet_mcp/api/`:

```python
# new_endpoint.py
from .base import BaseAPIClient
from .models.new_endpoint import NewItem, NewItemListRequest, NewItemListResponse

class NewEndpointClient(BaseAPIClient):
    async def list_items(self, **kwargs) -> NewItemListResponse:
        # Implementation
        pass
```

### 3. Register MCP Tools

Add tools in `src/albatross_kiotviet_mcp/server.py`:

```python
@self.mcp.tool()
async def list_new_items(params: NewItemListParams) -> Dict[str, Any]:
    """List new items."""
    # Implementation
    pass
```

### 4. Add Tests

Create test files in `tests/`:

```python
# test_new_endpoint.py
import pytest
from albatross_kiotviet_mcp.api.new_endpoint import NewEndpointClient

class TestNewEndpointClient:
    async def test_list_items(self):
        # Test implementation
        pass
```

## Configuration

### Environment Variables

All configuration is managed through environment variables:

```env
# Required
KIOTVIET_CLIENT_ID=your_client_id
KIOTVIET_CLIENT_SECRET=your_client_secret
KIOTVIET_RETAILER=your_retailer_name

# Optional
KIOTVIET_BASE_URL=https://public.kiotapi.com
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
LOG_LEVEL=INFO
DEBUG=false
TOKEN_CACHE_TTL=3600
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1.0
```

### Settings Validation

Settings are validated using Pydantic:

```python
from albatross_kiotviet_mcp.config import get_settings

settings = get_settings()  # Validates all settings
```

## Logging

### Structured Logging

The project uses structured logging with `structlog`:

```python
import structlog

logger = structlog.get_logger(__name__)
logger.info("Operation completed", user_id=123, duration=1.5)
```

### Log Levels

- **DEBUG**: Detailed information for debugging
- **INFO**: General information about operations
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failed operations
- **CRITICAL**: Critical errors that may cause system failure

## Error Handling

### Custom Exceptions

```python
from albatross_kiotviet_mcp.utils.exceptions import (
    KiotVietError,
    AuthenticationError,
    APIError,
    ConfigurationError,
    TokenExpiredError,
)

try:
    # API operation
    pass
except APIError as e:
    logger.error("API error", status_code=e.status_code, details=e.details)
```

### Retry Logic

API clients use `tenacity` for retry logic:

```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
)
async def api_operation():
    # Implementation
    pass
```

## Performance Considerations

### Token Caching

- Tokens are cached to avoid unnecessary authentication requests
- Cache includes expiration buffer to prevent using expired tokens
- Automatic refresh on token expiration

### HTTP Client Reuse

- HTTP clients are reused across requests
- Connection pooling for better performance
- Proper cleanup in async context managers

### Pagination

- Support for efficient pagination
- Configurable page sizes
- Cursor-based pagination support

## Security

### Credential Management

- Never hardcode credentials
- Use environment variables for all sensitive data
- Validate configuration on startup

### Token Security

- Tokens are stored in memory only
- Automatic token invalidation on errors
- Secure token refresh mechanism

## Deployment

### Docker

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .
RUN pip install -e .

CMD ["kiotviet-mcp"]
```

### Environment Setup

```bash
# Production environment
export KIOTVIET_CLIENT_ID=prod_client_id
export KIOTVIET_CLIENT_SECRET=prod_client_secret
export KIOTVIET_RETAILER=prod_retailer
export LOG_LEVEL=INFO
export DEBUG=false
```
