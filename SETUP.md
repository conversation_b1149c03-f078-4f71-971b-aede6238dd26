# Setup Instructions

## Prerequisites

- Python 3.10 or higher
- KiotViet API credentials (CLIENT_ID and CLIENT_SECRET)
- Retailer name for your KiotViet account

## Installation

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd albatross-kiotviet-mcp
```

### 2. Install with Python 3.10+

```bash
# If you have Python 3.10+
python3.10 -m pip install -e .

# Or if python3 points to 3.10+
python3 -m pip install -e .
```

### 3. Install Development Dependencies (Optional)

```bash
python3.10 -m pip install -e ".[dev]"
```

## Configuration

### 1. Create Environment File

```bash
cp .env.example .env
```

### 2. Edit .env with Your Credentials

```env
# KiotViet API Configuration
KIOTVIET_CLIENT_ID=your_actual_client_id
KIOTVIET_CLIENT_SECRET=your_actual_client_secret
KIOTVIET_RETAILER=your_retailer_name

# Optional Configuration
KIOTVIET_BASE_URL=https://public.kiotapi.com
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
LOG_LEVEL=INFO
TOKEN_CACHE_TTL=3600
```

### 3. Validate Configuration

```bash
python3.10 -m albatross_kiotviet_mcp.cli validate-config
```

## Running the Server

### Method 1: Direct Python Execution

```bash
python3.10 -m albatross_kiotviet_mcp.cli run
```

### Method 2: Using the Installed Command (if available)

```bash
kiotviet-mcp run
```

### Method 3: With Custom Settings

```bash
python3.10 -m albatross_kiotviet_mcp.cli run --log-level DEBUG --debug
```

## Testing the Installation

### 1. Run Validation Script

```bash
python3.10 scripts/validate_installation.py
```

### 2. Run Unit Tests

```bash
python3.10 -m pytest tests/unit/ -v
```

### 3. Check Version

```bash
python3.10 -m albatross_kiotviet_mcp.cli version
```

## Available MCP Tools

Once the server is running, it provides these MCP tools:

1. **list_categories** - List categories with pagination and filtering
2. **search_categories** - Search categories by name or code
3. **get_category** - Get a specific category by ID
4. **get_category_hierarchy** - Get categories in hierarchical structure

## Troubleshooting

### Python Version Issues

If you get errors about Python version:

```bash
# Check your Python version
python3 --version

# Find Python 3.10+
which python3.10 || which python3.11 || which python3.12

# Use the correct Python version
/path/to/python3.10 -m pip install -e .
```

### Import Errors

If you get import errors:

```bash
# Make sure you're using the same Python that installed the package
python3.10 -c "import albatross_kiotviet_mcp; print('OK')"
```

### Configuration Errors

If you get configuration validation errors:

```bash
# Check your .env file exists and has the required fields
cat .env

# Validate configuration
python3.10 -m albatross_kiotviet_mcp.cli validate-config
```

### API Connection Issues

If you get API connection errors:

1. Verify your KiotViet credentials are correct
2. Check your internet connection
3. Verify the API URLs are accessible
4. Check the server logs for detailed error messages

## Development

### Code Quality

```bash
# Format code
black src tests
isort src tests

# Type checking
mypy src

# Run all tests
python3.10 -m pytest
```

### Adding New API Endpoints

See `docs/DEVELOPMENT.md` for detailed instructions on extending the server with new KiotViet API endpoints.

## Support

For issues and questions:

1. Check the logs for detailed error messages
2. Verify your configuration with `validate-config`
3. Run the validation script to check installation
4. Review the API documentation in `docs/API.md`
