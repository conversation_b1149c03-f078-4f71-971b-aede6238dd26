#!/usr/bin/env python3
"""Validation script to test the installation and basic functionality."""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from albatross_kiotviet_mcp.config import get_settings, Settings
    from albatross_kiotviet_mcp.auth import AuthClient, TokenResponse, TokenCache
    from albatross_kiotviet_mcp.api import CategoriesClient
    from albatross_kiotviet_mcp.api.models import Category, CategoryListRequest
    from albatross_kiotviet_mcp.utils import setup_logging
    from albatross_kiotviet_mcp.utils.exceptions import KiotVietError
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def test_imports():
    """Test that all modules can be imported."""
    print("✅ All modules imported successfully")


def test_configuration():
    """Test configuration management."""
    try:
        # Test with minimal required settings
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
        )
        
        assert settings.kiotviet_client_id == "test_id"
        assert settings.kiotviet_base_url == "https://public.kiotapi.com"
        assert settings.log_level == "INFO"
        
        print("✅ Configuration validation passed")
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False
    
    return True


def test_models():
    """Test data models."""
    try:
        # Test TokenResponse
        token_response = TokenResponse(
            access_token="test_token",
            expires_in=3600,
        )
        assert token_response.token_type == "Bearer"
        
        # Test TokenCache
        token_cache = TokenCache.from_token_response(token_response)
        assert not token_cache.is_expired
        assert "Bearer test_token" == token_cache.authorization_header
        
        # Test Category
        category = Category(
            id=123,
            category_name="Test Category",
            has_child=False,
            is_active=True,
        )
        assert category.id == 123
        assert category.category_name == "Test Category"
        
        # Test CategoryListRequest
        request = CategoryListRequest(
            page_size=10,
            hierarchical_data=True,
        )
        assert request.page_size == 10
        assert request.hierarchical_data is True
        
        print("✅ Data models validation passed")
        
    except Exception as e:
        print(f"❌ Models test failed: {e}")
        return False
    
    return True


def test_logging():
    """Test logging setup."""
    try:
        logger = setup_logging(level="DEBUG", debug=True)
        logger.info("Test log message", test_field="test_value")
        
        print("✅ Logging setup passed")
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        return False
    
    return True


def test_exceptions():
    """Test custom exceptions."""
    try:
        # Test base exception
        error = KiotVietError("Test error", details={"key": "value"})
        assert error.message == "Test error"
        assert error.details == {"key": "value"}
        
        print("✅ Exception classes validation passed")
        
    except Exception as e:
        print(f"❌ Exceptions test failed: {e}")
        return False
    
    return True


async def test_client_initialization():
    """Test client initialization without making actual API calls."""
    try:
        settings = Settings(
            kiotviet_client_id="test_id",
            kiotviet_client_secret="test_secret",
            kiotviet_retailer="test_retailer",
        )
        
        # Test AuthClient initialization
        auth_client = AuthClient(settings)
        assert auth_client.settings == settings
        await auth_client.close()
        
        # Test CategoriesClient initialization
        auth_client = AuthClient(settings)
        categories_client = CategoriesClient(settings, auth_client)
        assert categories_client.settings == settings
        assert categories_client.auth_client == auth_client
        
        await categories_client.close()
        await auth_client.close()
        
        print("✅ Client initialization passed")
        
    except Exception as e:
        print(f"❌ Client initialization test failed: {e}")
        return False
    
    return True


async def main():
    """Run all validation tests."""
    print("🔍 Validating Albatross KiotViet MCP installation...\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("Configuration Tests", test_configuration),
        ("Model Tests", test_models),
        ("Logging Tests", test_logging),
        ("Exception Tests", test_exceptions),
        ("Client Initialization Tests", test_client_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
        
        print()
    
    print(f"📊 Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed! Installation is working correctly.")
        return 0
    else:
        print("⚠️  Some validation tests failed. Please check the installation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
