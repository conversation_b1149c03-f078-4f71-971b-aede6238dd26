<?xml version="1.0" ?>
<coverage version="7.9.2" timestamp="1752945577866" lines-valid="514" lines-covered="186" line-rate="0.3619" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.9.2 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/workspace/code/albatross-foundation/albatross-kiotviet-mcp</source>
	</sources>
	<packages>
		<package name="src.albatross_kiotviet_mcp" line-rate="0.01657" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
				<class name="cli.py" filename="src/albatross_kiotviet_mcp/cli.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="16" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="63" hits="0"/>
						<line number="69" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="146" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="153" hits="0"/>
						<line number="155" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
					</lines>
				</class>
				<class name="server.py" filename="src/albatross_kiotviet_mcp/server.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="37" hits="0"/>
						<line number="40" hits="0"/>
						<line number="43" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="50" hits="0"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="63" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0"/>
						<line number="95" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="128" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="163" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="184" hits="0"/>
						<line number="189" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0"/>
						<line number="200" hits="0"/>
						<line number="202" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="0"/>
						<line number="209" hits="0"/>
						<line number="211" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="227" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="233" hits="0"/>
						<line number="236" hits="0"/>
						<line number="239" hits="0"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="src.albatross_kiotviet_mcp.api" line-rate="0.3333" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/api/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
					</lines>
				</class>
				<class name="base.py" filename="src/albatross_kiotviet_mcp/api/base.py" complexity="0" line-rate="0.2903" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="48" hits="1"/>
						<line number="50" hits="0"/>
						<line number="52" hits="1"/>
						<line number="54" hits="0"/>
						<line number="56" hits="1"/>
						<line number="58" hits="0"/>
						<line number="60" hits="1"/>
						<line number="69" hits="0"/>
						<line number="71" hits="0"/>
						<line number="76" hits="1"/>
						<line number="81" hits="1"/>
						<line number="105" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="113" hits="0"/>
						<line number="115" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="141" hits="0"/>
						<line number="143" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="0"/>
						<line number="158" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="176" hits="0"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="194" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="210" hits="0"/>
					</lines>
				</class>
				<class name="categories.py" filename="src/albatross_kiotviet_mcp/api/categories.py" complexity="0" line-rate="0.3529" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="26" hits="1"/>
						<line number="51" hits="0"/>
						<line number="60" hits="0"/>
						<line number="70" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="0"/>
						<line number="86" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="1"/>
						<line number="107" hits="0"/>
						<line number="111" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="1"/>
						<line number="146" hits="0"/>
						<line number="154" hits="0"/>
						<line number="161" hits="1"/>
						<line number="178" hits="0"/>
						<line number="184" hits="0"/>
						<line number="192" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="src.albatross_kiotviet_mcp.api.models" line-rate="0.8814" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/api/models/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
					</lines>
				</class>
				<class name="categories.py" filename="src/albatross_kiotviet_mcp/api/models/categories.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="48" hits="1"/>
						<line number="52" hits="1"/>
						<line number="57" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
					</lines>
				</class>
				<class name="common.py" filename="src/albatross_kiotviet_mcp/api/models/common.py" complexity="0" line-rate="0.7742" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="0"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="src.albatross_kiotviet_mcp.auth" line-rate="0.4713" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/auth/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
					</lines>
				</class>
				<class name="client.py" filename="src/albatross_kiotviet_mcp/auth/client.py" complexity="0" line-rate="0.3036" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="43" hits="1"/>
						<line number="45" hits="0"/>
						<line number="47" hits="1"/>
						<line number="49" hits="0"/>
						<line number="51" hits="1"/>
						<line number="53" hits="0"/>
						<line number="55" hits="1"/>
						<line number="60" hits="1"/>
						<line number="69" hits="0"/>
						<line number="71" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="88" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="102" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="124" hits="1"/>
						<line number="133" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="143" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="148" hits="0"/>
						<line number="150" hits="1"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
					</lines>
				</class>
				<class name="models.py" filename="src/albatross_kiotviet_mcp/auth/models.py" complexity="0" line-rate="0.75" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="48" hits="0"/>
						<line number="52" hits="0"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="66" hits="0"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="75" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="src.albatross_kiotviet_mcp.config" line-rate="0.6964" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/config/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
					</lines>
				</class>
				<class name="settings.py" filename="src/albatross_kiotviet_mcp/config/settings.py" complexity="0" line-rate="0.6852" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="24" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="100" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="src.albatross_kiotviet_mcp.utils" line-rate="0.5625" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="src/albatross_kiotviet_mcp/utils/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
					</lines>
				</class>
				<class name="exceptions.py" filename="src/albatross_kiotviet_mcp/utils/exceptions.py" complexity="0" line-rate="0.625" branch-rate="0">
					<methods/>
					<lines>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
					</lines>
				</class>
				<class name="logging.py" filename="src/albatross_kiotviet_mcp/utils/logging.py" complexity="0" line-rate="0.3846" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="26" hits="0"/>
						<line number="33" hits="0"/>
						<line number="40" hits="0"/>
						<line number="42" hits="0"/>
						<line number="47" hits="0"/>
						<line number="52" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
