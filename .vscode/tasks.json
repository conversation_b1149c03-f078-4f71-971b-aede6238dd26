{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "python3.10", "args": ["-m", "pip", "install", "-e", ".[dev]"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Format Code", "type": "shell", "command": "black", "args": ["src", "tests", "scripts", "examples"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Sort Imports", "type": "shell", "command": "isort", "args": ["src", "tests", "scripts", "examples"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Type Check", "type": "shell", "command": "mypy", "args": ["src"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [{"owner": "mypy", "fileLocation": "absolute", "pattern": {"regexp": "^(.+?):(\\d+):(\\d+): (error|warning|note): (.+)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}]}, {"label": "Run Tests", "type": "shell", "command": "python3.10", "args": ["-m", "pytest", "tests/", "-v"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Tests with Coverage", "type": "shell", "command": "python3.10", "args": ["-m", "pytest", "tests/", "--cov", "--cov-report=html"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Validate Installation", "type": "shell", "command": "python3.10", "args": ["scripts/validate_installation.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "<PERSON>", "type": "shell", "command": "python3.10", "args": ["examples/demo.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Quality Check All", "dependsOrder": "sequence", "dependsOn": ["Format Code", "Sort Imports", "Type Check", "Run Tests"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}