{"version": "0.2.0", "configurations": [{"name": "Run MCP Server", "type": "python", "request": "launch", "module": "albatross_kiotviet_mcp.cli", "args": ["run", "--debug"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src"}, "envFile": "${workspaceFolder}/.env"}, {"name": "Validate Config", "type": "python", "request": "launch", "module": "albatross_kiotviet_mcp.cli", "args": ["validate-config"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src"}, "envFile": "${workspaceFolder}/.env"}, {"name": "<PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/examples/demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src"}, "envFile": "${workspaceFolder}/.env"}, {"name": "Run Validation Script", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/validate_installation.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src"}}, {"name": "Debug Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["tests/", "-v", "--tb=short"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src"}, "envFile": "${workspaceFolder}/.env"}]}