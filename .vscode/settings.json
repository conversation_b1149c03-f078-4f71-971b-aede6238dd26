{"python.defaultInterpreterPath": "/opt/homebrew/bin/python3.10", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.mypyEnabled": true, "python.linting.mypyArgs": ["--config-file=pyproject.toml"], "python.formatting.provider": "black", "python.formatting.blackArgs": ["--config=pyproject.toml"], "python.sortImports.args": ["--profile=black", "--settings-path=pyproject.toml"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.analysis.typeCheckingMode": "strict", "python.analysis.autoImportCompletions": true, "python.analysis.autoSearchPaths": true, "python.analysis.extraPaths": ["./src"], "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/htmlcov": true, "**/.coverage": true, "**/*.egg-info": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "editor.rulers": [88], "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on"}}