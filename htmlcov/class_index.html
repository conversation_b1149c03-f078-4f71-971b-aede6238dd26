<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:17 +0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85___init___py.html">src/albatross_kiotviet_mcp/__init__.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e___init___py.html">src/albatross_kiotviet_mcp/api/__init__.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t24">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t24"><data value='BaseAPIClient'>BaseAPIClient</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t13">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t13"><data value='CategoriesClient'>CategoriesClient</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb___init___py.html">src/albatross_kiotviet_mcp/api/models/__init__.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t12">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t12"><data value='Category'>Category</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t40">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t40"><data value='CategoryListRequest'>CategoryListRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t65">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html#t65"><data value='CategoryListResponse'>CategoryListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t9">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t9"><data value='OrderDirection'>OrderDirection</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t15">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t15"><data value='PaginationRequest'>PaginationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t31">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t31"><data value='PaginationResponse'>PaginationResponse</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8___init___py.html">src/albatross_kiotviet_mcp/auth/__init__.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t20">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t20"><data value='AuthClient'>AuthClient</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t9">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t9"><data value='TokenResponse'>TokenResponse</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t25">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t25"><data value='TokenCache'>TokenCache</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b___init___py.html">src/albatross_kiotviet_mcp/config/__init__.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t11">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t11"><data value='Settings'>Settings</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t17">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t17"><data value='CategoryListParams'>CategoryListParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t28">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t28"><data value='CategorySearchParams'>CategorySearchParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t37">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t37"><data value='CategoryGetParams'>CategoryGetParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t43">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t43"><data value='CategoryHierarchyParams'>CategoryHierarchyParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t50">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t50"><data value='KiotVietMCPServer'>KiotVietMCPServer</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d___init___py.html">src/albatross_kiotviet_mcp/utils/__init__.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t4">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t4"><data value='KiotVietError'>KiotVietError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t19">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t19"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t24">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t24"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t29">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t29"><data value='TokenExpiredError'>TokenExpiredError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t34">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t34"><data value='APIError'>APIError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html">src/albatross_kiotviet_mcp/utils/logging.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="5 13">38%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>514</td>
                <td>311</td>
                <td>0</td>
                <td class="right" data-ratio="203 514">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:17 +0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
