{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "9b3b503ba085d36a3a19c26fac850112", "files": {"z_0d93770799c78e85___init___py": {"hash": "a7ec11bbfdfa1a310b6cd6b14d12cb7b", "index": {"url": "z_0d93770799c78e85___init___py.html", "file": "src/albatross_kiotviet_mcp/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_163bde577557aa7e___init___py": {"hash": "5dbab6c31d740873f1c3e3d3a37593d9", "index": {"url": "z_163bde577557aa7e___init___py.html", "file": "src/albatross_kiotviet_mcp/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_163bde577557aa7e_base_py": {"hash": "a9385f9f640c65af9f1ffea6a2c7c081", "index": {"url": "z_163bde577557aa7e_base_py.html", "file": "src/albatross_kiotviet_mcp/api/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_163bde577557aa7e_categories_py": {"hash": "3204d5b38f94d2b14d399de28545e4be", "index": {"url": "z_163bde577557aa7e_categories_py.html", "file": "src/albatross_kiotviet_mcp/api/categories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_883f485a2f5edebb___init___py": {"hash": "b65366cd86eab51bce775c217b0f053c", "index": {"url": "z_883f485a2f5edebb___init___py.html", "file": "src/albatross_kiotviet_mcp/api/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_883f485a2f5edebb_categories_py": {"hash": "d2cb53f12f18991508ddaa1736254fde", "index": {"url": "z_883f485a2f5edebb_categories_py.html", "file": "src/albatross_kiotviet_mcp/api/models/categories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_883f485a2f5edebb_common_py": {"hash": "f44e0be9c48c97100aedcabe3b7343fa", "index": {"url": "z_883f485a2f5edebb_common_py.html", "file": "src/albatross_kiotviet_mcp/api/models/common.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6aab7392dcc07cb8___init___py": {"hash": "3547486fcdd09c6d9e494d051e6e6a22", "index": {"url": "z_6aab7392dcc07cb8___init___py.html", "file": "src/albatross_kiotviet_mcp/auth/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6aab7392dcc07cb8_client_py": {"hash": "36c974a0aba498687d9aef68a780db35", "index": {"url": "z_6aab7392dcc07cb8_client_py.html", "file": "src/albatross_kiotviet_mcp/auth/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6aab7392dcc07cb8_models_py": {"hash": "97b452fbfe25f2e4efccd6a274a601b6", "index": {"url": "z_6aab7392dcc07cb8_models_py.html", "file": "src/albatross_kiotviet_mcp/auth/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d93770799c78e85_cli_py": {"hash": "36a95597cf4ba5521be99e460e23532c", "index": {"url": "z_0d93770799c78e85_cli_py.html", "file": "src/albatross_kiotviet_mcp/cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dcf1eeac8c11343b___init___py": {"hash": "8f9421f71e61965dd35f02663e9a6a15", "index": {"url": "z_dcf1eeac8c11343b___init___py.html", "file": "src/albatross_kiotviet_mcp/config/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dcf1eeac8c11343b_settings_py": {"hash": "340e9d886ac0fcab6b07b5cf97b02b46", "index": {"url": "z_dcf1eeac8c11343b_settings_py.html", "file": "src/albatross_kiotviet_mcp/config/settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d93770799c78e85_server_py": {"hash": "df72f6d7960bc6c53589f9c82b807a26", "index": {"url": "z_0d93770799c78e85_server_py.html", "file": "src/albatross_kiotviet_mcp/server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9cebb8f51456f90d___init___py": {"hash": "9b73cb635a9a527bcb493b82520cd323", "index": {"url": "z_9cebb8f51456f90d___init___py.html", "file": "src/albatross_kiotviet_mcp/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9cebb8f51456f90d_exceptions_py": {"hash": "0d841aedce9a23eb03f6096981546708", "index": {"url": "z_9cebb8f51456f90d_exceptions_py.html", "file": "src/albatross_kiotviet_mcp/utils/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9cebb8f51456f90d_logging_py": {"hash": "fa1bfa3bd3a32b3259f007d9506a58b5", "index": {"url": "z_9cebb8f51456f90d_logging_py.html", "file": "src/albatross_kiotviet_mcp/utils/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}