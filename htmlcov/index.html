<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">36%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:19 +0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85___init___py.html">src/albatross_kiotviet_mcp/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e___init___py.html">src/albatross_kiotviet_mcp/api/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td>62</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="18 62">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td>34</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="12 34">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb___init___py.html">src/albatross_kiotviet_mcp/api/models/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td>31</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="24 31">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8___init___py.html">src/albatross_kiotviet_mcp/auth/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td>56</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="17 56">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td>28</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="21 28">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b___init___py.html">src/albatross_kiotviet_mcp/config/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td>54</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="37 54">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html">src/albatross_kiotviet_mcp/server.py</a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d___init___py.html">src/albatross_kiotviet_mcp/utils/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="10 16">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html">src/albatross_kiotviet_mcp/utils/logging.py</a></td>
                <td>13</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="5 13">38%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>514</td>
                <td>328</td>
                <td>0</td>
                <td class="right" data-ratio="186 514">36%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:19 +0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_9cebb8f51456f90d_logging_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_0d93770799c78e85___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
