<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">36%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:19 +0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85___init___py.html">src/albatross_kiotviet_mcp/__init__.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e___init___py.html">src/albatross_kiotviet_mcp/api/__init__.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t27">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t27"><data value='init__'>BaseAPIClient.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t48">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t48"><data value='aenter__'>BaseAPIClient.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t52">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t52"><data value='aexit__'>BaseAPIClient.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t56">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t56"><data value='close'>BaseAPIClient.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t60">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t60"><data value='get_headers'>BaseAPIClient._get_headers</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t81">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html#t81"><data value='make_request'>BaseAPIClient._make_request</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html">src/albatross_kiotviet_mcp/api/base.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t16">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t16"><data value='init__'>CategoriesClient.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t26">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t26"><data value='list_categories'>CategoriesClient.list_categories</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t95">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t95"><data value='get_category'>CategoriesClient.get_category</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t125">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t125"><data value='search_categories'>CategoriesClient.search_categories</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t161">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html#t161"><data value='get_category_hierarchy'>CategoriesClient.get_category_hierarchy</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html">src/albatross_kiotviet_mcp/api/categories.py</a></td>
                <td class="name left"><a href="z_163bde577557aa7e_categories_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb___init___py.html">src/albatross_kiotviet_mcp/api/models/__init__.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html">src/albatross_kiotviet_mcp/api/models/categories.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_categories_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t40">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t40"><data value='validate_total'>PaginationResponse.validate_total</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t47">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t47"><data value='has_more'>PaginationResponse.has_more</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t52">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html#t52"><data value='next_current_item'>PaginationResponse.next_current_item</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html">src/albatross_kiotviet_mcp/api/models/common.py</a></td>
                <td class="name left"><a href="z_883f485a2f5edebb_common_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8___init___py.html">src/albatross_kiotviet_mcp/auth/__init__.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t23">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t23"><data value='init__'>AuthClient.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t43">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t43"><data value='aenter__'>AuthClient.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t47">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t47"><data value='aexit__'>AuthClient.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t51">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t51"><data value='close'>AuthClient.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t60">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t60"><data value='request_token'>AuthClient._request_token</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t124">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t124"><data value='get_valid_token'>AuthClient.get_valid_token</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t150">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html#t150"><data value='invalidate_token'>AuthClient.invalidate_token</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html">src/albatross_kiotviet_mcp/auth/client.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t18">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t18"><data value='validate_expires_in'>TokenResponse.validate_expires_in</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t34">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t34"><data value='from_token_response'>TokenCache.from_token_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t60">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t60"><data value='is_expired'>TokenCache.is_expired</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t69">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html#t69"><data value='authorization_header'>TokenCache.authorization_header</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html">src/albatross_kiotviet_mcp/auth/models.py</a></td>
                <td class="name left"><a href="z_6aab7392dcc07cb8_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t24">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t24"><data value='run'>run</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t103">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t103"><data value='validate_config'>validate_config</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t144">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t144"><data value='version'>version</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t153">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html#t153"><data value='main'>main</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html">src/albatross_kiotviet_mcp/cli.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b___init___py.html">src/albatross_kiotviet_mcp/config/__init__.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t47">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t47"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t56">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t56"><data value='validate_token_cache_ttl'>Settings.validate_token_cache_ttl</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t64">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t64"><data value='validate_request_timeout'>Settings.validate_request_timeout</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t72">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t72"><data value='validate_max_retries'>Settings.validate_max_retries</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t80">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t80"><data value='validate_retry_delay'>Settings.validate_retry_delay</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t94">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html#t94"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html">src/albatross_kiotviet_mcp/config/settings.py</a></td>
                <td class="name left"><a href="z_dcf1eeac8c11343b_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t53">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t53"><data value='init__'>KiotVietMCPServer.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t72">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t72"><data value='register_tools'>KiotVietMCPServer._register_tools</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t76">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t76"><data value='list_categories'>KiotVietMCPServer._register_tools.list_categories</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t111">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t111"><data value='search_categories'>KiotVietMCPServer._register_tools.search_categories</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t145">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t145"><data value='get_category'>KiotVietMCPServer._register_tools.get_category</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t174">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t174"><data value='get_category_hierarchy'>KiotVietMCPServer._register_tools.get_category_hierarchy</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t200">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t200"><data value='setup'>KiotVietMCPServer.setup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t217">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t217"><data value='cleanup'>KiotVietMCPServer.cleanup</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t227">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t227"><data value='run'>KiotVietMCPServer.run</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t229">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t229"><data value='setup_and_run'>KiotVietMCPServer.run.setup_and_run</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t239">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html#t239"><data value='main'>main</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html">src/albatross_kiotviet_mcp/server.py</a></td>
                <td class="name left"><a href="z_0d93770799c78e85_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d___init___py.html">src/albatross_kiotviet_mcp/utils/__init__.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t7">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t7"><data value='init__'>KiotVietError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t37">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html#t37"><data value='init__'>APIError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html">src/albatross_kiotviet_mcp/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html#t10">src/albatross_kiotviet_mcp/utils/logging.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html#t10"><data value='setup_logging'>setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html">src/albatross_kiotviet_mcp/utils/logging.py</a></td>
                <td class="name left"><a href="z_9cebb8f51456f90d_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>514</td>
                <td>328</td>
                <td>0</td>
                <td class="right" data-ratio="186 514">36%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 00:19 +0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
