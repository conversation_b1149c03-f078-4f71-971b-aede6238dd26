"""Configuration settings for the KiotViet MCP server."""

import os
from functools import lru_cache
from typing import Optional

from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # KiotViet API Configuration
    kiotviet_client_id: str = Field(..., env="KIOTVIET_CLIENT_ID")
    kiotviet_client_secret: str = Field(..., env="KIOTVIET_CLIENT_SECRET")
    kiotviet_retailer: str = Field(..., env="KIOTVIET_RETAILER")
    
    # API URLs
    kiotviet_base_url: str = Field(
        default="https://public.kiotapi.com",
        env="KIOTVIET_BASE_URL"
    )
    kiotviet_auth_url: str = Field(
        default="https://id.kiotviet.vn/connect/token",
        env="KIOTVIET_AUTH_URL"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Token Management
    token_cache_ttl: int = Field(default=3600, env="TOKEN_CACHE_TTL")
    
    # HTTP Client Configuration
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_delay: float = Field(default=1.0, env="RETRY_DELAY")
    
    # MCP Server Configuration
    server_name: str = Field(default="kiotviet-mcp", env="SERVER_NAME")
    server_version: str = Field(default="0.1.0", env="SERVER_VERSION")

    @validator("log_level")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the standard levels."""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()

    @validator("token_cache_ttl")
    def validate_token_cache_ttl(cls, v: int) -> int:
        """Validate token cache TTL is positive."""
        if v <= 0:
            raise ValueError("Token cache TTL must be positive")
        return v

    @validator("request_timeout")
    def validate_request_timeout(cls, v: int) -> int:
        """Validate request timeout is positive."""
        if v <= 0:
            raise ValueError("Request timeout must be positive")
        return v

    @validator("max_retries")
    def validate_max_retries(cls, v: int) -> int:
        """Validate max retries is non-negative."""
        if v < 0:
            raise ValueError("Max retries must be non-negative")
        return v

    @validator("retry_delay")
    def validate_retry_delay(cls, v: float) -> float:
        """Validate retry delay is non-negative."""
        if v < 0:
            raise ValueError("Retry delay must be non-negative")
        return v

    class Config:
        """Pydantic configuration."""
        
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings.
    
    Returns:
        Settings: The application settings instance.
    """
    return Settings()
