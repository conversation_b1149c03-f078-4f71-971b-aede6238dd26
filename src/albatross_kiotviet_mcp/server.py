"""MCP server implementation for KiotViet API integration."""

import asyncio
from typing import Any, Dict, List, Optional

import structlog
from fastmcp import FastMCP
from pydantic import BaseModel, Field

from .api import CategoriesClient
from .auth import AuthClient
from .config import get_settings
from .utils import setup_logging
from .utils.exceptions import KiotVietError


class CategoryListParams(BaseModel):
    """Parameters for listing categories."""
    
    page_size: int = Field(default=20, ge=1, le=100, description="Number of items per page")
    current_item: int = Field(default=0, ge=0, description="Starting item index")
    hierarchical_data: bool = Field(default=False, description="Return hierarchical structure")
    include_inactive: bool = Field(default=False, description="Include inactive categories")
    parent_id: Optional[int] = Field(None, description="Filter by parent category ID")
    search_term: Optional[str] = Field(None, description="Search term for name or code")


class CategorySearchParams(BaseModel):
    """Parameters for searching categories."""
    
    search_term: str = Field(..., description="Search term for category name or code")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of items per page")
    current_item: int = Field(default=0, ge=0, description="Starting item index")
    include_inactive: bool = Field(default=False, description="Include inactive categories")


class CategoryGetParams(BaseModel):
    """Parameters for getting a specific category."""
    
    category_id: int = Field(..., description="Category ID to retrieve")


class CategoryHierarchyParams(BaseModel):
    """Parameters for getting category hierarchy."""
    
    parent_id: Optional[int] = Field(None, description="Parent category ID (None for root)")
    include_inactive: bool = Field(default=False, description="Include inactive categories")


class KiotVietMCPServer:
    """MCP server for KiotViet API integration."""
    
    def __init__(self) -> None:
        """Initialize the MCP server."""
        self.settings = get_settings()
        self.logger = setup_logging(
            level=self.settings.log_level,
            debug=self.settings.debug,
            logger_name="kiotviet-mcp-server"
        )
        
        # Initialize FastMCP
        self.mcp = FastMCP(
            name=self.settings.server_name,
            version=self.settings.server_version,
        )
        
        # Clients will be initialized in setup
        self.auth_client: Optional[AuthClient] = None
        self.categories_client: Optional[CategoriesClient] = None
        
        # Register tools
        self._register_tools()
    
    def _register_tools(self) -> None:
        """Register MCP tools."""
        
        @self.mcp.tool()
        async def list_categories(params: CategoryListParams) -> Dict[str, Any]:
            """List categories with optional filtering and pagination.
            
            Retrieve categories from KiotViet with support for pagination,
            hierarchical structure, and filtering options.
            """
            try:
                if not self.categories_client:
                    raise KiotVietError("Categories client not initialized")
                
                response = await self.categories_client.list_categories(
                    page_size=params.page_size,
                    current_item=params.current_item,
                    hierarchical_data=params.hierarchical_data,
                    include_inactive=params.include_inactive,
                    parent_id=params.parent_id,
                    search_term=params.search_term,
                )
                
                return {
                    "categories": [category.dict() for category in response.data],
                    "pagination": {
                        "total": response.total,
                        "page_size": response.page_size,
                        "current_item": response.current_item,
                        "has_more": response.has_more,
                        "next_current_item": response.next_current_item,
                    }
                }
                
            except Exception as e:
                self.logger.error("Error listing categories", error=str(e))
                raise KiotVietError(f"Failed to list categories: {e}")
        
        @self.mcp.tool()
        async def search_categories(params: CategorySearchParams) -> Dict[str, Any]:
            """Search categories by name or code.
            
            Search for categories matching the provided search term in
            category name or code fields.
            """
            try:
                if not self.categories_client:
                    raise KiotVietError("Categories client not initialized")
                
                response = await self.categories_client.search_categories(
                    search_term=params.search_term,
                    page_size=params.page_size,
                    current_item=params.current_item,
                    include_inactive=params.include_inactive,
                )
                
                return {
                    "categories": [category.dict() for category in response.data],
                    "search_term": params.search_term,
                    "pagination": {
                        "total": response.total,
                        "page_size": response.page_size,
                        "current_item": response.current_item,
                        "has_more": response.has_more,
                        "next_current_item": response.next_current_item,
                    }
                }
                
            except Exception as e:
                self.logger.error("Error searching categories", error=str(e))
                raise KiotVietError(f"Failed to search categories: {e}")
        
        @self.mcp.tool()
        async def get_category(params: CategoryGetParams) -> Dict[str, Any]:
            """Get a specific category by ID.
            
            Retrieve detailed information about a specific category
            using its unique identifier.
            """
            try:
                if not self.categories_client:
                    raise KiotVietError("Categories client not initialized")
                
                category = await self.categories_client.get_category(params.category_id)
                
                if category:
                    return {
                        "category": category.dict(),
                        "found": True,
                    }
                else:
                    return {
                        "category": None,
                        "found": False,
                        "message": f"Category with ID {params.category_id} not found",
                    }
                
            except Exception as e:
                self.logger.error("Error getting category", error=str(e))
                raise KiotVietError(f"Failed to get category: {e}")
        
        @self.mcp.tool()
        async def get_category_hierarchy(params: CategoryHierarchyParams) -> Dict[str, Any]:
            """Get categories in hierarchical structure.
            
            Retrieve categories organized in a hierarchical structure,
            optionally filtered by parent category.
            """
            try:
                if not self.categories_client:
                    raise KiotVietError("Categories client not initialized")
                
                categories = await self.categories_client.get_category_hierarchy(
                    parent_id=params.parent_id,
                    include_inactive=params.include_inactive,
                )
                
                return {
                    "categories": [category.dict() for category in categories],
                    "parent_id": params.parent_id,
                    "hierarchical": True,
                    "count": len(categories),
                }
                
            except Exception as e:
                self.logger.error("Error getting category hierarchy", error=str(e))
                raise KiotVietError(f"Failed to get category hierarchy: {e}")
    
    async def setup(self) -> None:
        """Set up the server and initialize clients."""
        self.logger.info("Setting up KiotViet MCP server")
        
        try:
            # Initialize authentication client
            self.auth_client = AuthClient(self.settings)
            
            # Initialize API clients
            self.categories_client = CategoriesClient(self.settings, self.auth_client)
            
            self.logger.info("KiotViet MCP server setup complete")
            
        except Exception as e:
            self.logger.error("Failed to setup server", error=str(e))
            raise
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        self.logger.info("Cleaning up KiotViet MCP server")
        
        if self.categories_client:
            await self.categories_client.close()
        
        if self.auth_client:
            await self.auth_client.close()
    
    async def run(self) -> None:
        """Run the MCP server."""
        try:
            await self.setup()
            self.logger.info("Starting KiotViet MCP server")
            await self.mcp.run()
        finally:
            await self.cleanup()


async def main() -> None:
    """Main entry point for the MCP server."""
    server = KiotVietMCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
