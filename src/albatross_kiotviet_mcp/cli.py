"""Command-line interface for the KiotViet MCP server."""

import asyncio
import sys
from typing import Optional

import typer
from dotenv import load_dotenv

from .config import get_settings
from .server import KiotVietMCPServer
from .utils import setup_logging
from .utils.exceptions import ConfigurationError, KiotVietError


app = typer.Typer(
    name="kiotviet-mcp",
    help="KiotViet MCP Server - Production-ready MCP server for KiotViet API integration",
    add_completion=False,
)


@app.command()
def run(
    env_file: Optional[str] = typer.Option(
        None,
        "--env-file",
        "-e",
        help="Path to environment file (default: .env)",
    ),
    log_level: Optional[str] = typer.Option(
        None,
        "--log-level",
        "-l",
        help="Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        "-d",
        help="Enable debug mode",
    ),
) -> None:
    """Run the KiotViet MCP server."""
    
    # Load environment variables
    if env_file:
        load_dotenv(env_file)
    else:
        load_dotenv()
    
    try:
        # Get settings
        settings = get_settings()
        
        # Override settings from CLI args
        if log_level:
            settings.log_level = log_level.upper()
        if debug:
            settings.debug = debug
        
        # Setup logging
        logger = setup_logging(
            level=settings.log_level,
            debug=settings.debug,
            logger_name="kiotviet-mcp-cli"
        )
        
        logger.info(
            "Starting KiotViet MCP server",
            server_name=settings.server_name,
            server_version=settings.server_version,
            log_level=settings.log_level,
            debug=settings.debug,
        )
        
        # Run the server
        server = KiotVietMCPServer()
        asyncio.run(server.run())
        
    except ConfigurationError as e:
        typer.echo(f"Configuration error: {e.message}", err=True)
        if e.details:
            typer.echo(f"Details: {e.details}", err=True)
        sys.exit(1)
        
    except KiotVietError as e:
        typer.echo(f"KiotViet error: {e.message}", err=True)
        if e.details:
            typer.echo(f"Details: {e.details}", err=True)
        sys.exit(1)
        
    except KeyboardInterrupt:
        typer.echo("Server stopped by user", err=True)
        sys.exit(0)
        
    except Exception as e:
        typer.echo(f"Unexpected error: {e}", err=True)
        sys.exit(1)


@app.command()
def validate_config(
    env_file: Optional[str] = typer.Option(
        None,
        "--env-file",
        "-e",
        help="Path to environment file (default: .env)",
    ),
) -> None:
    """Validate the configuration."""
    
    # Load environment variables
    if env_file:
        load_dotenv(env_file)
    else:
        load_dotenv()
    
    try:
        settings = get_settings()
        
        typer.echo("✅ Configuration is valid!")
        typer.echo(f"Server Name: {settings.server_name}")
        typer.echo(f"Server Version: {settings.server_version}")
        typer.echo(f"KiotViet Base URL: {settings.kiotviet_base_url}")
        typer.echo(f"KiotViet Auth URL: {settings.kiotviet_auth_url}")
        typer.echo(f"Retailer: {settings.kiotviet_retailer}")
        typer.echo(f"Log Level: {settings.log_level}")
        typer.echo(f"Debug Mode: {settings.debug}")
        typer.echo(f"Token Cache TTL: {settings.token_cache_ttl}s")
        
    except ConfigurationError as e:
        typer.echo(f"❌ Configuration error: {e.message}", err=True)
        if e.details:
            typer.echo(f"Details: {e.details}", err=True)
        sys.exit(1)
        
    except Exception as e:
        typer.echo(f"❌ Unexpected error: {e}", err=True)
        sys.exit(1)


@app.command()
def version() -> None:
    """Show version information."""
    from . import __version__, __author__, __email__
    
    typer.echo(f"KiotViet MCP Server v{__version__}")
    typer.echo(f"Author: {__author__}")
    typer.echo(f"Email: {__email__}")


def main() -> None:
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    main()
