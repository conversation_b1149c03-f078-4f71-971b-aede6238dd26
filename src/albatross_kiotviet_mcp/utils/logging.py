"""Logging configuration for the KiotViet MCP server."""

import logging
import sys
from typing import Optional

import structlog


def setup_logging(
    level: str = "INFO",
    debug: bool = False,
    logger_name: Optional[str] = None
) -> structlog.BoundLogger:
    """Set up structured logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        debug: Whether to enable debug mode
        logger_name: Optional logger name
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, level.upper()),
    )
    
    # Configure structlog
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    
    if debug:
        # In debug mode, use colorized console output
        processors.extend([
            structlog.dev.Console<PERSON>enderer(colors=True),
        ])
    else:
        # In production, use JSON output
        processors.extend([
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer(),
        ])
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Return a logger instance
    logger_name = logger_name or "kiotviet-mcp"
    return structlog.get_logger(logger_name)
