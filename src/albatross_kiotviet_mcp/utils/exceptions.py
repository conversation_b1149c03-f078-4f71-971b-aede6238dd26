"""Custom exceptions for the KiotViet MCP server."""


class KiotVietError(Exception):
    """Base exception for KiotViet MCP server errors."""
    
    def __init__(self, message: str, details: dict = None) -> None:
        """Initialize the exception.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ConfigurationError(KiotVietError):
    """Raised when there's a configuration error."""
    pass


class AuthenticationError(KiotVietError):
    """Raised when authentication fails."""
    pass


class TokenExpiredError(AuthenticationError):
    """Raised when an access token has expired."""
    pass


class APIError(KiotVietError):
    """Raised when an API request fails."""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = None, 
        response_data: dict = None,
        details: dict = None
    ) -> None:
        """Initialize the API error.
        
        Args:
            message: Error message
            status_code: HTTP status code
            response_data: Response data from the API
            details: Additional error details
        """
        super().__init__(message, details)
        self.status_code = status_code
        self.response_data = response_data or {}
