"""Authentication data models."""

from datetime import datetime, timed<PERSON>ta
from typing import Optional

from pydantic import BaseModel, Field, validator


class TokenResponse(BaseModel):
    """Response model for KiotViet token endpoint."""
    
    access_token: str
    token_type: str = "Bearer"
    expires_in: int  # seconds
    scope: Optional[str] = None
    
    @validator("expires_in")
    def validate_expires_in(cls, v: int) -> int:
        """Validate expires_in is positive."""
        if v <= 0:
            raise ValueError("expires_in must be positive")
        return v


class TokenCache(BaseModel):
    """Cached token with expiration tracking."""
    
    access_token: str
    token_type: str = "Bearer"
    expires_at: datetime
    scope: Optional[str] = None
    
    @classmethod
    def from_token_response(
        cls, 
        token_response: TokenResponse,
        buffer_seconds: int = 300  # 5 minutes buffer
    ) -> "TokenCache":
        """Create TokenCache from TokenResponse.
        
        Args:
            token_response: The token response from the API
            buffer_seconds: Buffer time before actual expiration
            
        Returns:
            TokenCache: Cached token instance
        """
        expires_at = datetime.utcnow() + timedelta(
            seconds=token_response.expires_in - buffer_seconds
        )
        
        return cls(
            access_token=token_response.access_token,
            token_type=token_response.token_type,
            expires_at=expires_at,
            scope=token_response.scope,
        )
    
    @property
    def is_expired(self) -> bool:
        """Check if the token is expired.
        
        Returns:
            bool: True if token is expired, False otherwise
        """
        return datetime.utcnow() >= self.expires_at
    
    @property
    def authorization_header(self) -> str:
        """Get the authorization header value.
        
        Returns:
            str: Authorization header value
        """
        return f"{self.token_type} {self.access_token}"
