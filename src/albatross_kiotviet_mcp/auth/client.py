"""Authentication client for KiotViet API."""

import asyncio
from typing import Optional

import httpx
import structlog
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from ..config import Settings
from ..utils.exceptions import AuthenticationError, TokenExpiredError
from .models import TokenResponse, TokenCache


class AuthClient:
    """Client for handling KiotViet authentication."""
    
    def __init__(self, settings: Settings) -> None:
        """Initialize the authentication client.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        self._token_cache: Optional[TokenCache] = None
        self._lock = asyncio.Lock()
        
        # HTTP client for authentication requests
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.settings.request_timeout),
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": f"{self.settings.server_name}/{self.settings.server_version}",
            }
        )
    
    async def __aenter__(self) -> "AuthClient":
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self._client.aclose()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError)),
    )
    async def _request_token(self) -> TokenResponse:
        """Request a new access token from KiotViet.
        
        Returns:
            TokenResponse: The token response
            
        Raises:
            AuthenticationError: If authentication fails
        """
        self.logger.info("Requesting new access token")
        
        data = {
            "scopes": "PublicApi.Access",
            "grant_type": "client_credentials",
            "client_id": self.settings.kiotviet_client_id,
            "client_secret": self.settings.kiotviet_client_secret,
        }
        
        try:
            response = await self._client.post(
                self.settings.kiotviet_auth_url,
                data=data,
            )
            response.raise_for_status()
            
            token_data = response.json()
            token_response = TokenResponse(**token_data)
            
            self.logger.info(
                "Successfully obtained access token",
                expires_in=token_response.expires_in,
                scope=token_response.scope,
            )
            
            return token_response
            
        except httpx.HTTPStatusError as e:
            self.logger.error(
                "HTTP error during token request",
                status_code=e.response.status_code,
                response_text=e.response.text,
            )
            raise AuthenticationError(
                f"Authentication failed with status {e.response.status_code}",
                details={
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                }
            ) from e
            
        except httpx.RequestError as e:
            self.logger.error("Request error during token request", error=str(e))
            raise AuthenticationError(
                f"Failed to connect to authentication server: {e}",
                details={"error": str(e)}
            ) from e
            
        except Exception as e:
            self.logger.error("Unexpected error during token request", error=str(e))
            raise AuthenticationError(
                f"Unexpected authentication error: {e}",
                details={"error": str(e)}
            ) from e
    
    async def get_valid_token(self) -> TokenCache:
        """Get a valid access token, refreshing if necessary.
        
        Returns:
            TokenCache: Valid cached token
            
        Raises:
            AuthenticationError: If unable to obtain a valid token
        """
        async with self._lock:
            # Check if we have a valid cached token
            if self._token_cache and not self._token_cache.is_expired:
                self.logger.debug("Using cached access token")
                return self._token_cache
            
            # Token is expired or doesn't exist, request a new one
            if self._token_cache and self._token_cache.is_expired:
                self.logger.info("Access token expired, requesting new token")
            else:
                self.logger.info("No cached token, requesting new token")
            
            token_response = await self._request_token()
            self._token_cache = TokenCache.from_token_response(token_response)
            
            return self._token_cache
    
    async def invalidate_token(self) -> None:
        """Invalidate the current cached token."""
        async with self._lock:
            self.logger.info("Invalidating cached token")
            self._token_cache = None
