"""Common API data models."""

from typing import Generic, List, Optional, TypeVar
from enum import Enum

from pydantic import BaseModel, Field, validator


class OrderDirection(str, Enum):
    """Order direction for API requests."""
    ASC = "Asc"
    DESC = "Desc"


class PaginationRequest(BaseModel):
    """Base pagination request model."""
    
    page_size: int = Field(default=20, ge=1, le=100, alias="pageSize")
    current_item: int = Field(default=0, ge=0, alias="currentItem")
    order_direction: OrderDirection = Field(
        default=OrderDirection.ASC, 
        alias="orderDirection"
    )
    
    model_config = {"populate_by_name": True}


T = TypeVar('T')


class PaginationResponse(BaseModel, Generic[T]):
    """Base pagination response model."""
    
    data: List[T] = Field(default_factory=list)
    total: int = Field(default=0, ge=0)
    page_size: int = Field(default=20, ge=1, alias="pageSize")
    current_item: int = Field(default=0, ge=0, alias="currentItem")
    
    @validator("total")
    def validate_total(cls, v: int) -> int:
        """Validate total is non-negative."""
        if v < 0:
            raise ValueError("Total must be non-negative")
        return v
    
    @property
    def has_more(self) -> bool:
        """Check if there are more items available."""
        return self.current_item + len(self.data) < self.total
    
    @property
    def next_current_item(self) -> Optional[int]:
        """Get the next current_item value for pagination."""
        if self.has_more:
            return self.current_item + len(self.data)
        return None
    
    model_config = {"populate_by_name": True}
