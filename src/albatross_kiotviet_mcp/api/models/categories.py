"""Categories API data models."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .common import PaginationRequest, PaginationResponse


class Category(BaseModel):
    """Category data model."""
    
    id: int = Field(..., description="Category ID")
    category_name: str = Field(..., alias="categoryName", description="Category name")
    category_code: Optional[str] = Field(
        None, alias="categoryCode", description="Category code"
    )
    description: Optional[str] = Field(None, description="Category description")
    parent_id: Optional[int] = Field(
        None, alias="parentId", description="Parent category ID"
    )
    has_child: bool = Field(
        default=False, alias="hasChild", description="Whether category has children"
    )
    is_active: bool = Field(
        default=True, alias="isActive", description="Whether category is active"
    )
    created_date: Optional[datetime] = Field(
        None, alias="createdDate", description="Creation date"
    )
    modified_date: Optional[datetime] = Field(
        None, alias="modifiedDate", description="Last modification date"
    )
    
    class Config:
        """Pydantic configuration."""
        allow_population_by_field_name = True


class CategoryListRequest(PaginationRequest):
    """Request model for listing categories."""
    
    hierarchical_data: bool = Field(
        default=False, 
        alias="hierachicalData",  # Note: KiotViet API has typo in field name
        description="Whether to return hierarchical data structure"
    )
    include_inactive: bool = Field(
        default=False,
        description="Whether to include inactive categories"
    )
    parent_id: Optional[int] = Field(
        None,
        alias="parentId", 
        description="Filter by parent category ID"
    )
    search_term: Optional[str] = Field(
        None,
        description="Search term for category name or code"
    )
    
    class Config:
        """Pydantic configuration."""
        allow_population_by_field_name = True


class CategoryListResponse(PaginationResponse[Category]):
    """Response model for category list."""
    
    data: List[Category] = Field(default_factory=list, description="List of categories")
    
    class Config:
        """Pydantic configuration."""
        allow_population_by_field_name = True
