"""Base API client for KiotViet integration."""

from typing import Any, Dict, Optional, Type, TypeVar, Union
import json

import httpx
import structlog
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from pydantic import BaseModel

from ..auth import AuthClient
from ..config import Settings
from ..utils.exceptions import APIError, AuthenticationError, TokenExpiredError


T = TypeVar('T', bound=BaseModel)


class BaseAPIClient:
    """Base client for KiotViet API operations."""
    
    def __init__(self, settings: Settings, auth_client: AuthClient) -> None:
        """Initialize the API client.
        
        Args:
            settings: Application settings
            auth_client: Authentication client
        """
        self.settings = settings
        self.auth_client = auth_client
        self.logger = structlog.get_logger(__name__)
        
        # HTTP client for API requests
        self._client = httpx.AsyncClient(
            base_url=self.settings.kiotviet_base_url,
            timeout=httpx.Timeout(self.settings.request_timeout),
            headers={
                "Content-Type": "application/json",
                "User-Agent": f"{self.settings.server_name}/{self.settings.server_version}",
            }
        )
    
    async def __aenter__(self) -> "BaseAPIClient":
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self._client.aclose()
    
    async def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests including authentication.
        
        Returns:
            Dict[str, str]: Headers for the request
            
        Raises:
            AuthenticationError: If unable to get valid token
        """
        token = await self.auth_client.get_valid_token()
        
        return {
            "Authorization": token.authorization_header,
            "Retailer": self.settings.kiotviet_retailer,
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.RequestError, TokenExpiredError)),
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Union[Dict[str, Any], BaseModel]] = None,
        params: Optional[Dict[str, Any]] = None,
        response_model: Optional[Type[T]] = None,
    ) -> Union[Dict[str, Any], T]:
        """Make an authenticated API request.
        
        Args:
            method: HTTP method
            endpoint: API endpoint (without base URL)
            data: Request data
            params: Query parameters
            response_model: Pydantic model for response parsing
            
        Returns:
            Union[Dict[str, Any], T]: Response data or parsed model
            
        Raises:
            APIError: If the API request fails
            AuthenticationError: If authentication fails
        """
        headers = await self._get_headers()
        
        # Prepare request data
        json_data = None
        if data:
            if isinstance(data, BaseModel):
                json_data = data.dict(by_alias=True, exclude_none=True)
            else:
                json_data = data
        
        self.logger.debug(
            "Making API request",
            method=method,
            endpoint=endpoint,
            has_data=json_data is not None,
            has_params=params is not None,
        )
        
        try:
            response = await self._client.request(
                method=method,
                url=endpoint,
                json=json_data,
                params=params,
                headers=headers,
            )
            
            # Handle authentication errors
            if response.status_code == 401:
                self.logger.warning("Received 401, invalidating token and retrying")
                await self.auth_client.invalidate_token()
                raise TokenExpiredError("Access token expired")
            
            response.raise_for_status()
            
            # Parse response
            response_data = response.json()
            
            self.logger.debug(
                "API request successful",
                method=method,
                endpoint=endpoint,
                status_code=response.status_code,
            )
            
            # Return parsed model if specified
            if response_model:
                return response_model(**response_data)
            
            return response_data
            
        except TokenExpiredError:
            # Re-raise token expired errors for retry
            raise
            
        except httpx.HTTPStatusError as e:
            self.logger.error(
                "HTTP error during API request",
                method=method,
                endpoint=endpoint,
                status_code=e.response.status_code,
                response_text=e.response.text,
            )
            
            # Try to parse error response
            error_data = {}
            try:
                error_data = e.response.json()
            except (json.JSONDecodeError, ValueError):
                pass
            
            raise APIError(
                f"API request failed with status {e.response.status_code}",
                status_code=e.response.status_code,
                response_data=error_data,
                details={
                    "method": method,
                    "endpoint": endpoint,
                    "response_text": e.response.text,
                }
            ) from e
            
        except httpx.RequestError as e:
            self.logger.error(
                "Request error during API request",
                method=method,
                endpoint=endpoint,
                error=str(e),
            )
            raise APIError(
                f"Failed to connect to API server: {e}",
                details={
                    "method": method,
                    "endpoint": endpoint,
                    "error": str(e),
                }
            ) from e
            
        except Exception as e:
            self.logger.error(
                "Unexpected error during API request",
                method=method,
                endpoint=endpoint,
                error=str(e),
            )
            raise APIError(
                f"Unexpected API error: {e}",
                details={
                    "method": method,
                    "endpoint": endpoint,
                    "error": str(e),
                }
            ) from e
