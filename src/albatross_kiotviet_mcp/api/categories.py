"""Categories API client for KiotViet integration."""

from typing import List, Optional

import structlog

from ..auth import AuthClient
from ..config import Settings
from .base import BaseAPIClient
from .models.categories import Category, CategoryListRequest, CategoryListResponse


class CategoriesClient(BaseAPIClient):
    """Client for KiotViet Categories API operations."""
    
    def __init__(self, settings: Settings, auth_client: AuthClient) -> None:
        """Initialize the categories client.
        
        Args:
            settings: Application settings
            auth_client: Authentication client
        """
        super().__init__(settings, auth_client)
        self.logger = structlog.get_logger(__name__)
    
    async def list_categories(
        self,
        page_size: int = 20,
        current_item: int = 0,
        hierarchical_data: bool = False,
        include_inactive: bool = False,
        parent_id: Optional[int] = None,
        search_term: Optional[str] = None,
    ) -> CategoryListResponse:
        """List categories with optional filtering and pagination.
        
        Args:
            page_size: Number of items per page (1-100)
            current_item: Starting item index for pagination
            hierarchical_data: Whether to return hierarchical structure
            include_inactive: Whether to include inactive categories
            parent_id: Filter by parent category ID
            search_term: Search term for category name or code
            
        Returns:
            CategoryListResponse: Paginated list of categories
            
        Raises:
            APIError: If the API request fails
        """
        request = CategoryListRequest(
            page_size=page_size,
            current_item=current_item,
            hierarchical_data=hierarchical_data,
            include_inactive=include_inactive,
            parent_id=parent_id,
            search_term=search_term,
        )
        
        self.logger.info(
            "Listing categories",
            page_size=page_size,
            current_item=current_item,
            hierarchical_data=hierarchical_data,
            include_inactive=include_inactive,
            parent_id=parent_id,
            search_term=search_term,
        )
        
        response_data = await self._make_request(
            method="POST",
            endpoint="/categories",
            data=request,
        )
        
        # Parse the response
        categories = [Category(**item) for item in response_data.get("data", [])]
        
        response = CategoryListResponse(
            data=categories,
            total=response_data.get("total", len(categories)),
            page_size=page_size,
            current_item=current_item,
        )
        
        self.logger.info(
            "Successfully retrieved categories",
            count=len(categories),
            total=response.total,
            has_more=response.has_more,
        )
        
        return response
    
    async def get_category(self, category_id: int) -> Optional[Category]:
        """Get a specific category by ID.
        
        Args:
            category_id: The category ID to retrieve
            
        Returns:
            Optional[Category]: The category if found, None otherwise
            
        Raises:
            APIError: If the API request fails
        """
        self.logger.info("Getting category", category_id=category_id)
        
        # Use list_categories with search to find specific category
        # KiotViet API doesn't have a direct get-by-id endpoint for categories
        response = await self.list_categories(
            page_size=1,
            current_item=0,
        )
        
        # Find the category with matching ID
        for category in response.data:
            if category.id == category_id:
                self.logger.info("Found category", category_id=category_id)
                return category
        
        self.logger.warning("Category not found", category_id=category_id)
        return None
    
    async def search_categories(
        self,
        search_term: str,
        page_size: int = 20,
        current_item: int = 0,
        include_inactive: bool = False,
    ) -> CategoryListResponse:
        """Search categories by name or code.
        
        Args:
            search_term: Search term for category name or code
            page_size: Number of items per page (1-100)
            current_item: Starting item index for pagination
            include_inactive: Whether to include inactive categories
            
        Returns:
            CategoryListResponse: Paginated list of matching categories
            
        Raises:
            APIError: If the API request fails
        """
        self.logger.info(
            "Searching categories",
            search_term=search_term,
            page_size=page_size,
            current_item=current_item,
            include_inactive=include_inactive,
        )
        
        return await self.list_categories(
            page_size=page_size,
            current_item=current_item,
            search_term=search_term,
            include_inactive=include_inactive,
        )
    
    async def get_category_hierarchy(
        self,
        parent_id: Optional[int] = None,
        include_inactive: bool = False,
    ) -> List[Category]:
        """Get categories in hierarchical structure.
        
        Args:
            parent_id: Parent category ID (None for root categories)
            include_inactive: Whether to include inactive categories
            
        Returns:
            List[Category]: List of categories in hierarchical order
            
        Raises:
            APIError: If the API request fails
        """
        self.logger.info(
            "Getting category hierarchy",
            parent_id=parent_id,
            include_inactive=include_inactive,
        )
        
        response = await self.list_categories(
            page_size=100,  # Get more items for hierarchy
            current_item=0,
            hierarchical_data=True,
            parent_id=parent_id,
            include_inactive=include_inactive,
        )
        
        return response.data
