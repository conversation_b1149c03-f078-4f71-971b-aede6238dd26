Metadata-Version: 2.4
Name: albatross-kiotviet-mcp
Version: 0.1.0
Summary: Production-ready MCP server for KiotViet Public API integration
Author-email: Albatross Foundation <<EMAIL>>
License: MIT
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.10
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: mcp>=1.0.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pydantic-settings>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: structlog>=23.0.0
Requires-Dist: tenacity>=8.0.0
Requires-Dist: typer>=0.9.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Dynamic: license-file

# Albatross KiotViet MCP Server

A production-ready MCP (Model Context Protocol) server that integrates with the KiotViet Public API using FastMCP framework.

## Features

- 🔐 **Secure Authentication**: Automatic token management with refresh and caching
- 🏗️ **Clean Architecture**: Modular design with separation of concerns
- 🚀 **Production Ready**: Comprehensive error handling, logging, and configuration
- 🔧 **Extensible**: Easy to add new KiotViet API endpoints
- 📊 **Type Safe**: Full type hints and Pydantic models
- 🧪 **Well Tested**: Comprehensive test suite

## Quick Start

### Prerequisites

- Python 3.9 or higher
- KiotViet API credentials (CLIENT_ID and CLIENT_SECRET)
- Retailer name for your KiotViet account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd albatross-kiotviet-mcp
```

2. Install dependencies:
```bash
pip install -e .
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your KiotViet credentials
```

4. Run the MCP server:
```bash
kiotviet-mcp
```

## Configuration

Create a `.env` file with the following variables:

```env
# KiotViet API Configuration
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here

# Optional Configuration
KIOTVIET_BASE_URL=https://public.kiotapi.com
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
LOG_LEVEL=INFO
TOKEN_CACHE_TTL=3600
```

## Project Structure

```
src/albatross_kiotviet_mcp/
├── __init__.py
├── cli.py                 # CLI entry point
├── server.py             # MCP server implementation
├── config/
│   ├── __init__.py
│   └── settings.py       # Configuration management
├── auth/
│   ├── __init__.py
│   ├── models.py         # Authentication data models
│   └── client.py         # Token management
├── api/
│   ├── __init__.py
│   ├── base.py           # Base API client
│   ├── categories.py     # Categories API implementation
│   └── models/
│       ├── __init__.py
│       ├── common.py     # Common API models
│       └── categories.py # Categories-specific models
└── utils/
    ├── __init__.py
    ├── logging.py        # Logging configuration
    └── exceptions.py     # Custom exceptions
```

## Available Tools

### Categories Management

- `list_categories`: Retrieve categories with pagination and filtering options
- `get_category`: Get detailed information about a specific category

## Development

### Setup Development Environment

```bash
# Install with development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov

# Run specific test types
pytest -m unit
pytest -m integration
```

### Code Quality

```bash
# Format code
black src tests
isort src tests

# Type checking
mypy src

# Run all quality checks
pre-commit run --all-files
```

## API Documentation

### Authentication

The server automatically handles authentication with KiotViet API:
- Obtains access tokens using client credentials
- Caches tokens to avoid unnecessary requests
- Automatically refreshes expired tokens
- Includes proper headers in all API requests

### Adding New Endpoints

To add a new KiotViet API endpoint:

1. Create a new module in `src/albatross_kiotviet_mcp/api/`
2. Define Pydantic models in `src/albatross_kiotviet_mcp/api/models/`
3. Implement the API client extending the base client
4. Register tools in the MCP server
5. Add tests for the new functionality

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run quality checks
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
